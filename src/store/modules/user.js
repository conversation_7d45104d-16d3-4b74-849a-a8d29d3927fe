/*
 * @Author: ff.liu <EMAIL>
 * @Date: 2024-10-09 14:48:18
 * LastEditors: yuelanfeng<PERSON>
 * LastEditTime: 2024-11-06 18:05:43
 * @Description:
 */
// Common,
import { DateUtils } from '@hexinfo/gf-ui';
import GFIndexApi from '../../api/gfApi/GFIndexApi';
import Vue from 'vue';
import Cookies from 'js-cookie';
import SMCrypto from 'sm-crypto';
import GFUserApi from "../../api/gfApi/GFUserApi";

import { store } from '@hexinfo/ares-ui';

const dataProcessing = (resp, commit, resolve) => {
    if (!resp || !resp.ok) {
        resolve(resp);
        return;
    }
    commit('resetUser');
    //储存用户信息
    commit('setUser', resp.data);
    resolve(resp);
}

const user = {
    state: {
        empId: '',
        loginUserId: '',
        userName: '',
        userId: '',
        orgId: '',
        loginSNum: 0,
        tenantId: '',
        roles: [],
        menus: [],
        permissions: [],
        btnPermission: [],
        appConfig: {},
        staChange: false,
        staExpired: false,
        attrs: {},
        userIdList: []
    },
    mutations: {
        setUser: (state, userInfo) => {
            const roles = userInfo.roles || [];
            state.empId = userInfo.attrs.empId;
            state.loginUserId = userInfo.attrs.loginUserId;
            state.userIdList = userInfo.attrs.userIdList;
            state.userName = userInfo.userName;
            state.userId = userInfo.userId;
            state.orgId = userInfo.orgId;
            state.tenantId = userInfo.tenantId;
            state.loginSNum = userInfo.loginSNum;
            state.roles = roles;
            state.menus = store.state.user.menus;
            state.permissions = userInfo.permissions;
            state.btnPermission = userInfo.btnPermission;
            state.appConfig = userInfo.attrs.appConfig;
            const dictMap = userInfo.attrs.dictList;
            state.staChange = userInfo.attrs.changeAuth;
            state.staExpired = userInfo.attrs.staExpired;
            state.attrs = userInfo.attrs;
            let security = Vue.prototype.$app.options.security;
            security.publicKey = state.attrs.publicKey || state.appConfig.publicKey;
            if (state.attrs.appKey && state.attrs.appSignCode) {
                sessionStorage.setItem('gf.appKey', state.attrs.appKey);
                const appSignCode = SMCrypto.sm4.decrypt(state.attrs.appSignCode, security.crypto);
                sessionStorage.setItem('gf.appSignCode', appSignCode);
            }
            sessionStorage.setItem('token', userInfo.attrs.token);
            Cookies.set('token', userInfo.attrs.token)
            sessionStorage.setItem('tenantId', userInfo.tenantId || '');
            // 是否有权限修改密码
            let showChangePwd = Vue.prototype.$hasPermission('gf.system.common.pwd');
            // 只有应用管理员才拥有切换身份的权限
            let showSwitchIdentity = roles.includes('admin') && Vue.prototype.$hasPermission('gf.system.switch.identity');
            // 是否有工作台
            let showWorkbench = Vue.prototype.$hasPermission('gf.system.workbench');
            // 是否有个人操作日志
            let showOperateLog = Vue.prototype.$hasPermission('gf.personal.log');

            userInfo.sex = userInfo.attrs.sex;
            //上一次登录的时间
            let lastLoginSuccessTs = userInfo.attrs.lastLoginSuccessTs;
            if (typeof lastLoginSuccessTs == 'number') {
                lastLoginSuccessTs = DateUtils.formatDate(new Date(lastLoginSuccessTs), 'yyyy-MM-dd HH:mm:ss');
            }
            let lastLoginSuccessIp = userInfo.attrs.lastLoginSuccessIp;
            console.log("hahahh" , userInfo.attrs.rolesName);
            Vue.prototype.$app.onLogin({
                user: userInfo,
                dictMap: dictMap,
                roleMap: userInfo.attrs.rolesName,
                menus: state.menus,
                userNavMenus: [
                    { title: '上次登录时间', command: '', icon: 'gf-iconfont gf-a-Group2471', time: lastLoginSuccessTs },
                    { title: '上次登录的IP', command: '', icon: 'el-icon-location-information', time: lastLoginSuccessIp },
                    {
                        title: '工作台',
                        hidden: !showWorkbench,
                        command: 'gf.dashboardSetting',
                        divided: true,
                        icon: 'ares-iconfont ares-gongzuotai'
                    },
                    {
                        title: '修改密码',
                        hidden: !showChangePwd,
                        command: 'gf.changePwd',
                        divided: !showWorkbench,
                        icon: 'ares-iconfont ares-key'
                    },
                    {
                        title: '切换租户',
                        hidden: !showSwitchIdentity,
                        command: 'gf.switch.identity',
                        divided: !showWorkbench && !showChangePwd,
                        icon: 'gf-iconfont gf-a-Group2476'
                    },
                    {
                        title: '操作日志',
                        hidden: !showOperateLog,
                        command: 'gf.personalLog',
                        divided: !showWorkbench && !showChangePwd && !showSwitchIdentity,
                        icon: 'ares-iconfont ares-document'
                    },
                    // {
                    //     title: '导航模式',
                    //     command: 'gf.switch.layout',
                    //     divided: !showWorkbench && !showChangePwd && !showSwitchIdentity && !showOperateLog,
                    //     icon: 'ares-iconfont ares-layout-fill'
                    // },
                    {
                        title: '注销登录',
                        command: 'gf.logout',
                        divided: true,
                        icon: 'gf-iconfont gf-a-Group2477',
                        class: 'gf-logout'
                    }
                ]
            });
        },
        resetUser: (state) => {
            state.empId = '';
            state.loginUserId = '';
            state.userName = '';
            state.userId = '';
            state.orgId = '';
            state.tenantId = '';
            state.loginSNum = 0;
            state.roles = [];
            state.userIdList = [];
            state.menus = [];
            state.btnPermission = [];
            state.appConfig = {};
            state.staChange = false;
            state.staExpired = false;
            state.attrs = {};
            sessionStorage.clear();
            Cookies.remove("token");
            Vue.prototype.$app.onLogout();
        }
    },
    actions: {
        switchId({ commit }, pkId) {
            return new Promise((resolve) => {
                GFUserApi.switchUserId({ pkId: pkId }).then((resp) => {
                    dataProcessing(resp, commit, resolve);
                }).catch(error => {
                    resolve(error);
                });
            });
        },

        login({ commit }, params) {
            return new Promise((resolve, reject) => {
                GFIndexApi.login(params.data, params.publicKey).then(
                    (resp) => {
                        let homeLogo = sessionStorage.getItem("gf.homeLogo")
                        let headIcon = sessionStorage.getItem("gf.headIcon")
                        let headTitle = sessionStorage.getItem("gf.headTitle")
                        dataProcessing(resp, commit, resolve);
                        sessionStorage.setItem("gf.homeLogo", homeLogo);
                        sessionStorage.setItem("gf.headIcon", headIcon);
                        sessionStorage.setItem("gf.headTitle", headTitle);
                    },
                    (error) => {
                        reject(error);
                    }
                );
            });
        },
        logout({ commit }) {
            return new Promise((resolve) => {
                GFIndexApi.logout().then(
                    resp => {
                        if (resp && resp.ok && resp.data) {
                            sessionStorage.setItem("gf.app.login.custom-logout-url", resp.data);
                        } else {
                            sessionStorage.removeItem("gf.app.login.custom-logout-url");
                        }
                        commit('resetUser');
                        resolve(resp.data);
                    },
                    () => {
                        commit('resetUser');
                        resolve();
                    }
                );
            });
        },
        getUserInfo({ commit }) {
            return new Promise((resolve) => {
                GFIndexApi.getUserInfo().then((resp) => {
                    console.log("resp################-", resp);
                    if (resp.data) {
                        //储存用户信息
                        commit('setUser', resp.data);
                    }
                    resolve(resp);
                }).catch(error => {
                    resolve(error);
                });
            });
        }
    }
};

export default user;
