<svg fill="none" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 52" class="design-iconfont">
  <g filter="url(#u6s2an41k__filter0_d_460_598)">
    <path d="M24 6L41.3205 16V36L24 46L6.67949 36V16L24 6Z" fill="#fff"/>
  </g>
  <path d="M30.5092 35.1152C30.3393 34.9453 30.1205 34.8242 29.8764 34.7754C29.7299 34.7461 29.6244 34.6211 29.6244 34.4727V27.293C29.6244 24.1875 27.0834 21.6133 23.9779 21.625C20.8822 21.6367 18.3744 24.1504 18.3744 27.25V34.4727C18.3744 34.6211 18.2689 34.7461 18.1225 34.7754C17.5521 34.8926 17.1244 35.3965 17.1244 36H30.8744C30.8744 35.6543 30.7338 35.3418 30.5092 35.1152ZM25.0795 25.0625L24.7006 28.4473H26.1869L22.9213 33.1875L23.3002 29.8027H21.8119L25.0795 25.0625ZM23.9994 20.0625C23.6537 20.0625 23.3744 19.7832 23.3744 19.4375V16.625C23.3744 16.2793 23.6537 16 23.9994 16C24.3451 16 24.6244 16.2793 24.6244 16.625V19.4375C24.6244 19.7832 24.3451 20.0625 23.9994 20.0625ZM19.4682 21.7832C19.1693 21.9551 18.7865 21.8535 18.6146 21.5547L17.2084 19.1191C17.0365 18.8203 17.1381 18.4375 17.4369 18.2656C17.7357 18.0938 18.1186 18.1953 18.2904 18.4941L19.6967 20.9297C19.8686 21.2285 19.767 21.6094 19.4682 21.7832ZM28.5307 21.7832C28.8295 21.9551 29.2123 21.8535 29.3842 21.5547L30.7904 19.1191C30.9623 18.8203 30.8607 18.4375 30.5619 18.2656C30.2631 18.0938 29.8803 18.1953 29.7084 18.4941L28.3021 20.9297C28.1303 21.2285 28.2318 21.6094 28.5307 21.7832ZM17.9545 24.6895C17.8646 25.0234 17.5228 25.2207 17.1889 25.1309L14.4721 24.4043C14.1381 24.3145 13.9408 23.9727 14.0287 23.6387C14.1186 23.3047 14.4603 23.1074 14.7943 23.1973L17.5111 23.9258C17.8451 24.0137 18.0424 24.3555 17.9545 24.6895ZM30.0443 24.6895C30.1342 25.0234 30.476 25.2207 30.81 25.1309L33.5268 24.4023C33.8607 24.3125 34.058 23.9707 33.9682 23.6367C33.8783 23.3027 33.5365 23.1055 33.2025 23.1953L30.4857 23.9238C30.1537 24.0137 29.9564 24.3555 30.0443 24.6895Z" fill="url(#u6s2an41k__paint0_linear_460_598)"/>
  <defs>
    <linearGradient id="u6s2an41k__paint0_linear_460_598" x1="23.9988" y1="16" x2="23.9988" y2="36" gradientUnits="userSpaceOnUse">
      <stop offset=".135417" stop-color="#E84D4D"/>
      <stop offset="1" stop-color="#FFD56D"/>
    </linearGradient>
    <filter id="u6s2an41k__filter0_d_460_598" x=".679443" y="0" width="46.6411" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="3"/>
      <feComposite in2="hardAlpha" operator="out"/>
      <feColorMatrix values="0 0 0 0 0.983333 0 0 0 0 0.894365 0 0 0 0 0.893194 0 0 0 1 0"/>
      <feBlend in2="BackgroundImageFix" result="effect1_dropShadow_460_598"/>
      <feBlend in="SourceGraphic" in2="effect1_dropShadow_460_598" result="shape"/>
    </filter>
  </defs>
</svg>
