<svg width="120" height="120" viewBox="0 0 120 120" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M58.3034 106.193C33.1601 106.193 12.7773 85.7788 12.7773 60.5966C12.7773 35.4143 33.1601 15 58.3034 15C83.4467 15 103.829 35.4143 103.829 60.5966C103.829 85.7788 83.4467 106.193 58.3034 106.193Z" fill="#F5F8FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.3979 33.8867C13.1951 34.2782 11.9022 33.6193 11.5101 32.415C11.1181 31.2107 11.7754 29.917 12.9782 29.5254C14.181 29.1339 15.4739 29.7928 15.8659 30.9971C16.258 32.2014 15.6007 33.4951 14.3979 33.8867Z" fill="#EAEEF9"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.3961 26.4054C18.5782 26.6717 17.699 26.2236 17.4324 25.4047C17.1658 24.5858 17.6128 23.706 18.4307 23.4398C19.2486 23.1735 20.1278 23.6215 20.3943 24.4405C20.6609 25.2594 20.214 26.1392 19.3961 26.4054Z" fill="#EAEEF9"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M104.229 35.3505C102.81 36.6494 100.607 36.5517 99.3077 35.1324C98.0087 33.7131 98.106 31.5096 99.5251 30.2107C100.944 28.9118 103.148 29.0095 104.447 30.4288C105.746 31.8481 105.648 34.0516 104.229 35.3505Z" fill="#EAEEF9"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M101.852 85.127C101.255 85.6739 100.327 85.6328 99.7799 85.0352C99.233 84.4376 99.2739 83.5098 99.8715 82.9629C100.469 82.416 101.397 82.4571 101.944 83.0547C102.491 83.6523 102.45 84.5801 101.852 85.127Z" fill="#EAEEF9"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M23.6782 98.4979C22.6664 98.4979 21.8462 97.6764 21.8462 96.663C21.8462 95.6496 22.6664 94.8281 23.6782 94.8281C24.69 94.8281 25.5103 95.6496 25.5103 96.663C25.5103 97.6764 24.69 98.4979 23.6782 98.4979Z" fill="url(#paint0_linear_420_3254)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M108.264 44.9358C107.455 44.9358 106.799 44.2786 106.799 43.4679C106.799 42.6572 107.455 42 108.264 42C109.074 42 109.73 42.6572 109.73 43.4679C109.73 44.2786 109.074 44.9358 108.264 44.9358Z" fill="url(#paint1_linear_420_3254)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M102.795 88.5999C102.354 88.5999 101.995 88.2417 101.995 87.7999C101.995 87.3581 102.354 87 102.795 87C103.237 87 103.595 87.3581 103.595 87.7999C103.595 88.2417 103.237 88.5999 102.795 88.5999Z" fill="url(#paint2_linear_420_3254)"/>
<g filter="url(#filter0_d_420_3254)">
<path d="M88.0742 94.9097C87.5246 96.2858 85.9674 96.928 84.6849 96.3776L45.7542 81.0564C44.3802 80.5059 43.739 78.9463 44.2886 77.6619L64.5326 26.102C65.0822 24.7258 66.6394 24.0836 67.9218 24.6341L96.1351 35.7351L103.372 54.6342L88.0742 94.9097Z" fill="#F9FAFD"/>
</g>
<path d="M62.4258 70.507C62.0594 70.4153 61.9678 70.0483 62.0594 69.6813C62.151 69.3143 62.5174 69.2226 62.8838 69.3143L86.2423 78.4887C86.6087 78.5804 86.7003 78.9474 86.6087 79.3144C86.4255 79.5896 86.0591 79.7731 85.7843 79.6814L62.4258 70.507Z" fill="#E3EAF2"/>
<path d="M55.3723 67.6633C55.0059 67.5715 54.9143 67.2045 55.0059 66.8376C55.0975 66.4706 55.4639 66.3788 55.8304 66.4706L59.9524 68.122C60.3188 68.2137 60.4104 68.5807 60.3188 68.9477C60.2272 69.3146 59.8608 69.4064 59.4944 69.3146L55.3723 67.6633Z" fill="#E3EAF2"/>
<path d="M77.7232 69.2179C77.3568 69.1262 77.2652 68.7592 77.3568 68.3923C77.4484 68.0253 77.8148 67.9335 78.1812 68.0253L88.6238 72.1537C88.9902 72.2455 89.0818 72.6125 88.9902 72.9794C88.8986 73.3464 88.5322 73.4381 88.1658 73.3464L77.7232 69.2179Z" fill="#E3EAF2"/>
<path d="M58.1206 61.5148C57.7542 61.4231 57.6626 61.0561 57.7542 60.6891C57.8458 60.3222 58.2122 60.2304 58.5786 60.3222L73.7845 66.2855C74.1509 66.3772 74.2425 66.7442 74.1509 67.1112C74.0593 67.4781 73.6929 67.5699 73.3265 67.4782L58.1206 61.5148Z" fill="#E3EAF2"/>
<path d="M86.792 65.5539C86.4256 65.4621 86.334 65.0952 86.4256 64.7282C86.5172 64.3612 86.8836 64.2695 87.25 64.3612L91.2805 65.9209C91.6469 66.0126 91.7385 66.3796 91.6469 66.7466C91.5553 67.1135 91.1889 67.2053 90.8225 67.1135L86.792 65.5539Z" fill="#E3EAF2"/>
<path d="M60.7769 55.1945C60.4105 55.1028 60.3189 54.7358 60.4105 54.3688C60.5021 54.0018 60.8685 53.9101 61.2349 54.0018L83.4941 62.8092C83.8605 62.901 83.9521 63.2679 83.8605 63.6349C83.7689 64.0019 83.4025 64.1854 83.0361 64.0019L60.7769 55.1945Z" fill="#E3EAF2"/>
<path d="M63.9829 79.0461C63.6165 78.9543 63.5249 78.5874 63.6165 78.2204C63.7081 77.8534 64.0745 77.7617 64.441 77.8534L65.815 78.4039C66.1814 78.4956 66.273 78.8626 66.1814 79.2296C65.9982 79.5965 65.6318 79.6883 65.357 79.5965L63.9829 79.0461Z" fill="#E3EAF2"/>
<path d="M53.1739 74.7258C52.8075 74.634 52.7159 74.267 52.8075 73.9001C52.8991 73.5331 53.2655 73.4413 53.6319 73.5331L61.876 76.8359C62.2424 76.9276 62.334 77.2946 62.2424 77.6616C62.1508 78.0285 61.6928 78.1203 61.418 78.0285L53.1739 74.7258Z" fill="#E3EAF2"/>
<path d="M79.189 55.1945C78.8226 55.1028 78.731 54.7358 78.8226 54.3688C78.9142 54.0018 79.2806 53.9101 79.647 54.0018L94.0285 59.6899C94.3949 59.7817 94.4865 60.1487 94.3949 60.5156C94.3033 60.8826 93.8453 60.9744 93.5705 60.8826L79.189 55.1945Z" fill="#E3EAF2"/>
<path d="M63.4336 48.9445C63.0672 48.8528 62.9756 48.4858 63.0672 48.1188C63.1588 47.7518 63.5252 47.6601 63.8916 47.7518L74.9754 52.2473C75.3419 52.339 75.4335 52.706 75.3419 53.073C75.2502 53.4399 74.8838 53.5317 74.5174 53.3482L63.4336 48.9445Z" fill="#E3EAF2"/>
<path d="M69.2958 34.9112C68.8378 34.7277 68.6546 34.269 68.8378 33.8103C69.021 33.3516 69.479 33.1681 69.937 33.3516L88.6238 40.6911C89.0818 40.8745 89.265 41.3333 89.0818 41.792C88.8986 42.2507 88.4405 42.4342 87.9825 42.2507L69.2958 34.9112Z" fill="#CED7E2"/>
<path d="M67.5556 39.3018C67.0975 39.1183 66.9143 38.6596 67.0975 38.2009C67.2808 37.7422 67.7388 37.5587 68.1968 37.7422L75.3417 40.5862C75.7997 40.7697 75.9829 41.2284 75.7997 41.6872C75.6165 42.1459 75.0669 42.3294 74.7005 42.1459L67.5556 39.3018Z" fill="#CED7E2"/>
<path d="M96.1352 35.7344L91.8299 46.7436C91.1887 48.3032 92.1047 50.1381 93.5703 50.6886L103.372 54.5418" fill="#E6EDF5"/>
<g filter="url(#filter1_d_420_3254)">
<path d="M77.998 76.4705C78.5476 77.8466 77.998 79.4063 76.624 79.9567L38.3345 96.8376C36.9605 97.388 35.4032 96.8376 34.8536 95.4614L12.5944 44.819C12.0448 43.4428 12.5944 41.8832 13.9684 41.3327L41.7237 29.0391L60.1357 37.296L77.998 76.4705Z" fill="#FCFDFF"/>
</g>
<path d="M42.5481 77.5693C42.2733 77.7527 41.9069 77.5693 41.7236 77.294C41.5404 77.0188 41.7236 76.6518 41.9985 76.4683L64.9905 66.2848C65.2653 66.1013 65.6317 66.2848 65.8149 66.56C65.9981 66.8353 65.8149 67.294 65.5401 67.3857L42.5481 77.5693Z" fill="#E3EAF2"/>
<path d="M35.6779 80.5117C35.4031 80.6951 35.0367 80.5117 34.8535 80.2364C34.6703 79.9612 34.8535 79.5942 35.1283 79.4107L39.1588 77.6676C39.4336 77.4841 39.8 77.6676 39.9832 77.9428C40.1664 78.3098 40.0748 78.6768 39.7084 78.7685L35.6779 80.5117Z" fill="#E3EAF2"/>
<path d="M52.4411 65.7327C52.1663 65.9162 51.7999 65.7327 51.6167 65.4575C51.4335 65.1823 51.6167 64.8153 51.8915 64.6318L62.1509 60.1364C62.4257 59.9529 62.7921 60.1364 62.9753 60.4116C63.1585 60.6868 63.0669 61.0538 62.7005 61.2373L52.4411 65.7327Z" fill="#E3EAF2"/>
<path d="M33.2048 74.2725C32.93 74.456 32.5636 74.2725 32.3804 73.9973C32.1972 73.722 32.3804 73.3551 32.6552 73.1716L47.5863 66.566C47.8611 66.3826 48.2275 66.566 48.4107 66.8413C48.5939 67.2083 48.5023 67.5752 48.1359 67.667L33.2048 74.2725Z" fill="#E3EAF2"/>
<path d="M56.2883 56.6601C56.0135 56.8436 55.6471 56.6601 55.4639 56.3849C55.2807 56.1096 55.4639 55.7427 55.7387 55.5592L59.6776 53.816C59.9524 53.6326 60.3188 53.816 60.502 54.0913C60.6852 54.4583 60.502 54.8252 60.2272 54.917L56.2883 56.6601Z" fill="#E3EAF2"/>
<path d="M30.6401 68.0324C30.3653 68.2159 29.9989 68.0324 29.8157 67.7572C29.6325 67.482 29.8157 67.115 30.0905 66.9315L52.0749 57.2067C52.3497 57.0232 52.7161 57.2067 52.8993 57.4819C52.9909 57.8489 52.8993 58.2159 52.5329 58.3076L30.6401 68.0324Z" fill="#E3EAF2"/>
<path d="M49.7844 82.5357C49.5096 82.7192 49.1432 82.5357 48.96 82.2605C48.7768 81.9853 48.96 81.6183 49.2348 81.4348L50.6088 80.7926C50.8836 80.6091 51.25 80.7926 51.4332 81.0678C51.6164 81.3431 51.4332 81.71 51.1584 81.8935L49.7844 82.5357Z" fill="#E3EAF2"/>
<path d="M39.0676 87.2059C38.7928 87.3894 38.4264 87.2059 38.2432 86.9307C38.06 86.6554 38.2432 86.2885 38.518 86.105L46.6705 82.527C46.9453 82.3435 47.3117 82.527 47.495 82.8022C47.6782 83.0774 47.5866 83.4444 47.2201 83.6279L39.0676 87.2059Z" fill="#E3EAF2"/>
<path d="M43.5559 54.9133C43.2811 55.0968 42.9147 54.9133 42.7315 54.6381C42.5483 54.3629 42.7315 53.9959 43.0063 53.8124L57.1129 47.5739C57.3877 47.3904 57.7541 47.5739 57.9373 47.8491C58.1206 48.1243 57.9373 48.4913 57.6625 48.6748L43.5559 54.9133Z" fill="#E3EAF2"/>
<path d="M28.0749 61.6955C27.8001 61.8789 27.4337 61.6954 27.2505 61.4202C27.0673 61.145 27.2505 60.778 27.5253 60.5945L38.4259 55.8239C38.7007 55.6404 39.0671 55.8239 39.2503 56.0991C39.4335 56.3743 39.3419 56.7413 38.9755 56.9248L28.0749 61.6955Z" fill="#E3EAF2"/>
<path d="M22.1211 47.5628C21.6631 47.7463 21.2051 47.5628 21.0219 47.104C20.8387 46.6453 21.0219 46.1866 21.4799 46.0031L39.8002 37.9297C40.2582 37.7462 40.7162 37.9297 40.8994 38.3884C41.0826 38.8471 40.8994 39.3058 40.533 39.4893L22.1211 47.5628Z" fill="#CED7E2"/>
<path d="M24.0447 52.0617C23.5867 52.2452 23.1287 52.0617 22.9454 51.603C22.7622 51.1443 22.9454 50.6856 23.4035 50.5021L30.4568 47.3828C30.9148 47.1993 31.3728 47.3828 31.556 47.8415C31.7392 48.2085 31.556 48.759 31.098 48.9425L24.0447 52.0617Z" fill="#CED7E2"/>
<path d="M41.7236 29.1328L46.4869 39.9586C47.1281 41.4265 49.0518 42.1604 50.5174 41.5182L60.1356 37.298" fill="#E6EDF5"/>
<g filter="url(#filter2_d_420_3254)">
<path d="M88.8984 95.3632C88.8984 97.1981 87.4328 98.7577 85.5091 98.7577H32.1053C30.2733 98.7577 28.7161 97.2898 28.7161 95.3632V24.6289C28.7161 22.794 30.1817 21.2344 32.1053 21.2344H70.8529L88.2572 40.317L88.8984 95.3632Z" fill="white"/>
</g>
<path d="M52.8073 87.7529C52.3493 87.7529 51.9829 87.386 51.9829 86.9273C51.9829 86.4685 52.3493 86.1016 52.8073 86.1016H54.731C55.189 86.1016 55.5554 86.4685 55.5554 86.9273C55.5554 87.386 55.189 87.7529 54.731 87.7529H52.8073Z" fill="#E3EAF2"/>
<path d="M37.968 87.7529C37.51 87.7529 37.1436 87.386 37.1436 86.9273C37.1436 86.4685 37.51 86.1016 37.968 86.1016H49.3266C49.7846 86.1016 50.151 86.4685 50.151 86.9273C50.151 87.386 49.7846 87.7529 49.3266 87.7529H37.968Z" fill="#E3EAF2"/>
<path d="M38.5177 32.7956C37.9681 32.7956 37.4185 32.3369 37.4185 31.6947C37.4185 31.1442 37.8765 30.5938 38.5177 30.5938H64.1662C64.7158 30.5938 65.2654 31.0525 65.2654 31.6947C65.1738 32.3369 64.7158 32.7956 64.1662 32.7956H38.5177Z" fill="#CED7E2"/>
<path d="M38.5177 38.9362C37.9681 38.9362 37.4185 38.4775 37.4185 37.8353C37.4185 37.2848 37.8765 36.7344 38.5177 36.7344H48.3191C48.8687 36.7344 49.4183 37.1931 49.4183 37.8353C49.3267 38.4775 48.8687 38.9362 48.3191 38.9362H38.5177Z" fill="#CED7E2"/>
<path d="M70.8525 21.3281V36.4658C70.8525 38.5759 72.6846 40.319 74.7914 40.319H88.2569" fill="#E6EDF5"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M66.456 50.5C66.456 50.5 66.456 50.5 66.3644 50.5C66.3644 50.5 66.3644 50.5 66.2728 50.5H52.5325C52.5325 50.5 52.5325 50.5 52.4409 50.5C52.4409 50.5 52.4409 50.5 52.3493 50.5H39.4335C37.9679 50.5 36.8687 51.6927 36.8687 53.1606V76.4634C36.8687 77.9313 38.0595 79.124 39.4335 79.124H79.7383C81.2039 79.124 82.3031 77.9313 82.3031 76.4634V53.1606C82.3947 51.6927 81.2039 50.5 79.8299 50.5H66.456ZM53.2654 65.9129H65.6316V71.1423H53.2654V65.9129ZM65.6316 77.5644H53.2654V72.7019H65.6316V77.5644ZM65.6316 64.2615H53.2654V59.0322H65.6316V64.2615ZM53.2654 52.0596H65.6316V57.4725H53.2654V52.0596ZM67.1888 64.2615V59.0322H80.8375V64.2615H67.1888ZM38.4259 64.2615V59.0322H51.6165V64.2615H51.7081H38.4259ZM51.6165 65.9129V71.1423H38.4259V65.9129H51.6165ZM80.8375 65.9129V71.1423H67.1888V65.9129H80.8375ZM80.8375 57.4725H67.1888V52.0596H79.8299C80.3795 52.0596 80.8375 52.5184 80.8375 53.1606V57.4725ZM51.6165 52.0596V57.4725H38.4259V53.1606C38.4259 52.5184 38.8839 52.0596 39.4335 52.0596H51.6165ZM38.4259 72.7019H51.6165V77.5644H39.4335C38.8839 77.5644 38.4259 77.1056 38.4259 76.4634V72.7019ZM67.1888 77.5644V72.7019H80.8375V76.4634C80.8375 77.1056 80.3795 77.5644 79.8299 77.5644H67.1888Z" fill="#E3EAF2"/>
<path d="M87.3685 19.5928C87.3685 19.6292 87.3685 19.6292 87.3685 19.6656C87.0053 21.1945 85.8795 22.4686 84.4632 23.0147C84.3905 23.0511 84.3542 23.0511 84.2816 23.0875C83.8095 23.2695 83.301 23.3423 82.7563 23.3423C80.1415 23.3423 77.9988 21.1945 77.9988 18.5735C77.9988 15.9525 80.1415 13.8047 82.7563 13.8047C85.3711 13.8047 87.4774 15.9161 87.4774 18.5371C87.4774 18.9011 87.4411 19.2288 87.3685 19.5928Z" fill="url(#paint3_linear_420_3254)"/>
<path d="M100.963 92.8747C99.0785 98.3264 94.555 102.65 88.9948 104.248C87.3927 104.718 85.6963 105 84 105C74.1047 104.906 66 96.9165 66 86.953C66 76.9896 74.1047 69 84 69C93.8953 69 102 77.0836 102 86.953C102 89.0209 101.623 90.9948 100.963 92.8747Z" fill="url(#paint4_linear_420_3254)"/>
<path d="M77.4672 87.8115C77.17 88.6711 76.4567 89.3529 75.58 89.6048C75.3273 89.6789 75.0599 89.7234 74.7924 89.7234C73.2321 89.7086 71.9541 88.4488 71.9541 86.8777C71.9541 85.3067 73.2321 84.0469 74.7924 84.0469C76.3527 84.0469 77.6306 85.3215 77.6306 86.8777C77.6306 87.2038 77.5712 87.515 77.4672 87.8115Z" fill="url(#paint5_linear_420_3254)"/>
<path d="M86.9269 87.8115C86.6297 88.6711 85.9164 89.3529 85.0397 89.6048C84.787 89.6789 84.5196 89.7234 84.2521 89.7234C82.6918 89.7086 81.4138 88.4488 81.4138 86.8777C81.4138 85.3067 82.6918 84.0469 84.2521 84.0469C85.8124 84.0469 87.0904 85.3215 87.0904 86.8777C87.0904 87.2038 87.0309 87.515 86.9269 87.8115Z" fill="url(#paint6_linear_420_3254)"/>
<path d="M96.3898 87.8115C96.0926 88.6711 95.3793 89.3529 94.5026 89.6048C94.2499 89.6789 93.9825 89.7234 93.715 89.7234C92.1547 89.7086 90.8767 88.4488 90.8767 86.8777C90.8767 85.3067 92.1547 84.0469 93.715 84.0469C95.2753 84.0469 96.5532 85.3215 96.5532 86.8777C96.5532 87.2038 96.4938 87.515 96.3898 87.8115Z" fill="url(#paint7_linear_420_3254)"/>
<defs>
<filter id="filter0_d_420_3254" x="36.0923" y="18.4375" width="75.2793" height="88.1328" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.776471 0 0 0 0 0.866667 0 0 0 0 0.941176 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_420_3254"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_420_3254" result="shape"/>
</filter>
<filter id="filter1_d_420_3254" x="4.38379" y="23.0391" width="81.825" height="84.0078" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.862745 0 0 0 0 0.917647 0 0 0 0 0.968627 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_420_3254"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_420_3254" result="shape"/>
</filter>
<filter id="filter2_d_420_3254" x="20.7161" y="15.2344" width="76.1824" height="93.5234" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.776471 0 0 0 0 0.866667 0 0 0 0 0.941176 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_420_3254"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_420_3254" result="shape"/>
</filter>
<linearGradient id="paint0_linear_420_3254" x1="21.9139" y1="98.486" x2="25.5244" y2="98.486" gradientUnits="userSpaceOnUse">
<stop offset="0.239583" stop-color="#C2FFEC"/>
<stop offset="1" stop-color="#40DBAB"/>
</linearGradient>
<linearGradient id="paint1_linear_420_3254" x1="106.833" y1="44.8931" x2="109.737" y2="44.8931" gradientUnits="userSpaceOnUse">
<stop offset="0.09375" stop-color="#FFD39D"/>
<stop offset="1" stop-color="#F7AA4D"/>
</linearGradient>
<linearGradient id="paint2_linear_420_3254" x1="102.014" y1="88.5766" x2="103.599" y2="88.5766" gradientUnits="userSpaceOnUse">
<stop offset="0.09375" stop-color="#FFD39D"/>
<stop offset="1" stop-color="#F7AA4D"/>
</linearGradient>
<linearGradient id="paint3_linear_420_3254" x1="78.1108" y1="23.2035" x2="87.4998" y2="23.2035" gradientUnits="userSpaceOnUse">
<stop offset="0.09375" stop-color="#FFD39D"/>
<stop offset="1" stop-color="#FFA63A"/>
</linearGradient>
<linearGradient id="paint4_linear_420_3254" x1="66.0151" y1="104.991" x2="101.997" y2="104.991" gradientUnits="userSpaceOnUse">
<stop offset="0.0208333" stop-color="#74A6FD"/>
<stop offset="0.640625" stop-color="#3178F4"/>
<stop offset="1" stop-color="#2770EE"/>
</linearGradient>
<linearGradient id="paint5_linear_420_3254" x1="78.5525" y1="87.3408" x2="75.2393" y2="84.0379" gradientUnits="userSpaceOnUse">
<stop offset="0.0113208" stop-color="#C2D1FF"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint6_linear_420_3254" x1="88.0122" y1="87.3408" x2="84.699" y2="84.0379" gradientUnits="userSpaceOnUse">
<stop offset="0.0113208" stop-color="#C2D1FF"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint7_linear_420_3254" x1="97.4751" y1="87.3408" x2="94.1619" y2="84.0379" gradientUnits="userSpaceOnUse">
<stop offset="0.0113208" stop-color="#C2D1FF"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
